<?php

declare(strict_types=1);

namespace App\Services;

use App\Classes\Constants\ServerParameters;
use App\Classes\ValueObjects\RawContentObject;
use App\Exceptions\APIException;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;

class HeadlessBrowserService {
    public function __construct(private Client $client) {
    }

    /**
     * @throws APIException
     *
     * @return RawContentObject raw html or rss feed
     */
    public function getRawHtmlOrRss(string $endpointUrl, ?string $customUserAgent = null): RawContentObject {
        try {
            $response = $this->makeRequest(array_filter([
                'url'        => $endpointUrl,
                'user_agent' => $customUserAgent,
            ]));

            $content = $response->getBody()->getContents();
            if (empty($content)) {
                throw new APIException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, 'Headless Browser Service returned empty response for URL: ' . $endpointUrl);
            }

            return new RawContentObject($content);
        } catch (Exception $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * @throws APIException
     */
    private function makeRequest(array $requestData): ResponseInterface {
        try {
            $headlessBrowserServiceUrl = config('headless_browser.service_url');

            $response =  $this->client->request(ServerParameters::HTTP_METHOD_POST, $headlessBrowserServiceUrl, [
                'headers' => [
                    'Content-Type'  => 'application/json',
                ],
                'json' => $requestData,
            ]);

            return $response;
        } catch (GuzzleException $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }
}
