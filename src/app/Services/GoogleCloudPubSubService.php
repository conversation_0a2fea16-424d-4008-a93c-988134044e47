<?php

declare(strict_types=1);

namespace App\Services;

use App\Events\PubSubEvent;
use Google\Cloud\PubSub\PubSubClient;

/**
 * @codeCoverageIgnore
 */
class GoogleCloudPubSubService {
    protected PubSubClient $pubSubClient;

    public function __construct() {
        $credentialsJson = base64_decode(config('google.pubsub.key_value'), true);

        $this->pubSubClient = new PubSubClient([
            'credentials' => json_decode($credentialsJson, true),
        ]);
    }

    public function sendMessage(PubSubEvent $pubSubEvent): void {
        $topic = $this->pubSubClient->topic($pubSubEvent->getTopic());

        $topic->publish([
            'data'       => json_encode($pubSubEvent->getPayload()),
        ]);
    }
}
