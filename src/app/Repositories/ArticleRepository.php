<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Classes\ValueObjects\ArticleObject;
use App\Models\Article;

/**
 * @extends BaseRepository<Article>
 */
class ArticleRepository extends BaseRepository {
    public function __construct(Article $model) {
        parent::__construct($model);
    }

    public function createArticleFromObject(ArticleObject $articleObject): Article {
        return $this->create([
            'articleID'          => $articleObject->getArticleId(),
            'uniqueID'           => $articleObject->getUniqueId(),
            'channelID'          => $articleObject->getChannelId(),
            'title'              => $articleObject->getTitle(),
            'description'        => $articleObject->getDescription(),
            'html'               => $articleObject->getHtml(),
            'author'             => $articleObject->getAuthor(),
            'publishedDate'      => $articleObject->getPublishedDate(),
            'modifiedDate'       => $articleObject->getModifiedDate(),
            'url'                => $articleObject->getUrl(),
            'canonicalURL'       => $articleObject->getCanonicalURL(),
            'media'              => $articleObject->getMedia(),
            'categoryID'         => 0,
            'categoryIDArray'    => '',
            'tags'               => '',
            'tagsName'           => '',
            'permalink'          => $articleObject->getPermalink(),
            'content_md5'        => $articleObject->getContentMd5(),
            'relatedArticle'     => $articleObject->getRelatedArticle(),
            'default_topics'     => null,
            'viewCount'          => 0,
            'reactionCount'      => 0,
            'commentCount'       => 0,
            'keywordDescription' => '',
            'keywordHTML'        => '',
            'segmentTopicIds'    => '',
            'isOnline'           => 1,
            'isFixed'            => 0,
            'total_reposts'      => 0,
        ]);
    }

    public function updateArticleFromObject(ArticleObject $articleObject): bool {
        return $this->update('uniqueID', $articleObject->getUniqueId(), [
            'articleID'       => $articleObject->getArticleId(),
            'channelID'       => $articleObject->getChannelId(),
            'title'           => $articleObject->getTitle(),
            'description'     => $articleObject->getDescription(),
            'html'            => $articleObject->getHtml(),
            'author'          => $articleObject->getAuthor(),
            'publishedDate'   => $articleObject->getPublishedDate(),
            'modifiedDate'    => $articleObject->getModifiedDate(),
            'url'             => $articleObject->getUrl(),
            'canonicalURL'    => $articleObject->getCanonicalURL(),
            'media'           => $articleObject->getMedia(),
            'permalink'       => $articleObject->getPermalink(),
            'content_md5'     => $articleObject->getContentMd5(),
        ]);
    }
}
