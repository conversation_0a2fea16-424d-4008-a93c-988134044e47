<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Classes\Services\Criteria\Criteriable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Collection;
use Illuminate\Support\LazyCollection;

/**
 * @template  TModel of \Illuminate\Database\Eloquent\Model
 */
class BaseRepository {
    /**
     * @var TModel
     */
    public Model $model;

    public function __construct(Model $model) {
        $this->model = $model;
    }

    /**
     * @return Collection<int, TModel>
     */
    public function all(): Collection {
        return $this->getNewQuery()->get()->values();
    }

    /**
     * @return ?TModel
     */
    public function find(mixed $id): ?Model {
        return $this->getNewQuery()->find($id);
    }

    /**
     * @throws ModelNotFoundException
     *
     * @return TModel
     */
    public function findBy(string $field, mixed $value): Model {
        $model = $this->getNewQuery()->where($field, '=', $value)->first();
        if ($model === null) {
            throw new ModelNotFoundException(sprintf('Unable to find record where %s = %s in model %s', $field, $value, get_class($this->model)));
        }

        return $model;
    }

    /**
     * @return ?TModel
     */
    public function findWhere(array $values): ?Model {
        return $this->findAllWhere($values)->first();
    }

    /**
     * @return Collection<int, TModel>
     */
    public function findAllWhere(array $values): Collection {
        $query = $this->getNewQuery();

        foreach ($values as $field => $value) {
            $query->where($field, $value);
        }

        return $query->get()->values();
    }

    /**
     * @return Collection<int, TModel>
     */
    public function findAllWhereIn(array $ids, string $column = 'id'): Collection {
        return $this->getNewQuery()->whereIn($column, $ids)->get()->values();
    }

    /**
     * @return Collection<int, TModel>
     */
    public function findAllBasedOnCriteria(Criteriable ...$criteria): Collection {
        // extract all the applicable classes from the criteria
        $collection = new Collection($criteria);

        // if there is no criteria provided, return all rows
        if ($collection->count() === 0) {
            return $this->all();
        }

        /** @noinspection PhpUnnecessaryLocalVariableInspection */
        $results = $this->getQueryFromCriteria($collection)->get();

        return $results;
    }

    /**
     * Lazy collections are useful for when there are thousands of models to be kept in memory.
     *
     * @return LazyCollection<int, TModel>
     */
    public function getLazyCollectionBasedOnCriteria(Criteriable ...$criteria): LazyCollection {
        $collection = new Collection($criteria);
        if ($collection->count() === 0) {
            return $this->cursor();
        }

        return $this->getQueryFromCriteria($collection)->cursor();
    }

    public function getBuilderBasedOnCriteria(Criteriable ...$criteria): Builder {
        $collection = new Collection($criteria);
        if ($collection->count() === 0) {
            return $this->model->newQuery();
        }

        /** @noinspection PhpUnnecessaryLocalVariableInspection */
        return $this->getQueryFromCriteria($collection);
    }

    /**
     * @param array<string, mixed> $attributes
     *
     * @return TModel
     */
    public function create(array $attributes): Model {
        $model    = $this->model;
        $instance = new $model();
        $instance->fill($attributes)->save();

        return $instance;
    }

    /**
     * @param mixed $value
     *
     * @throws ModelNotFoundException
     */
    public function update(string $field, $value, array $data = []): bool {
        return $this->findBy($field, $value)->update($data);
    }

    public function delete(string $field, mixed $value): void {
        try {
            $model = $this->findBy($field, $value);
            $model->delete();
        } catch (ModelNotFoundException $exception) {
        }
    }

    public function make(array $with = []): Builder {
        return $this->model::with($with);
    }

    /**
     * @return LazyCollection<int, TModel>
     */
    public function cursor(): LazyCollection {
        return $this->getNewQuery()->cursor();
    }

    /**
     * @phpstan-param Collection<Criteriable> $collection
     */
    protected function getQueryFromCriteria(Collection $collection): Builder {
        return $collection->reduce(fn (Builder $query, Criteriable $criteriable) => $criteriable->apply($query), $this->model->newQuery());
    }

    private function getNewQuery(): Builder {
        return $this->model->newQuery();
    }
}
