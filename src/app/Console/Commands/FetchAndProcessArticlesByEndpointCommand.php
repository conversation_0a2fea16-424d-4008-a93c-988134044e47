<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Exceptions\FetcherCommandException;
use App\Modules\Fetcher\Logics\FetchAndProcessEndpointForLocalFetcherLogic;
use Illuminate\Console\Command;
use Throwable;

class FetchAndProcessArticlesByEndpointCommand extends Command {
    protected $signature = 'fetch-and-process-articles-by-endpoint {publisherEndpointId}';

    protected $description = 'Fetches content from configured endpoints according to publisher endpoint id, extracts article data, and prepares it for processing by the Parser Adder service';

    public function __construct(
        private FetchAndProcessEndpointForLocalFetcherLogic $fetchAndProcessEndpointForLocalFetcherLogic,
    ) {
        parent::__construct();
    }

    public function handle(): void {
        $publisherEndpointId = $this->argument('publisherEndpointId');

        try {
            $this->fetchAndProcessEndpointForLocalFetcherLogic->execute($publisherEndpointId);
        } catch (Throwable $e) {
            throw new FetcherCommandException($e->getCode(), $e->getMessage() . ' Endpoint ID: ' . $publisherEndpointId);
        }
    }
}
