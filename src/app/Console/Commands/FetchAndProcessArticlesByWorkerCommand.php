<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Exceptions\FetcherCommandException;
use App\Modules\Fetcher\Logics\FetchAndProcessEndpointFoWorkerLogic;
use Illuminate\Console\Command;
use Throwable;

class FetchAndProcessArticlesByWorkerCommand extends Command {
    protected $signature = 'fetch-and-process-articles-by-worker {workerId}';

    protected $description = 'Fetches content from configured endpoints according to worker id, extracts article data, and prepares it for processing by the Parser Adder service';

    public function __construct(
        private FetchAndProcessEndpointFoWorkerLogic $fetchAndProcessEndpointFoWorkerLogic,
    ) {
        parent::__construct();
    }

    public function handle(): void {
        $workerId = $this->argument('workerId');

        try {
            $this->fetchAndProcessEndpointFoWorkerLogic->execute($workerId);
        } catch (Throwable $e) {
            throw new FetcherCommandException($e->getCode(), $e->getMessage() . ' Worker ID: ' . $workerId);
        }
    }
}
