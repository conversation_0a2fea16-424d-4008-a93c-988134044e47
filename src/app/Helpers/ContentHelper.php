<?php

declare(strict_types=1);

namespace App\Helpers;

use Exception;

class ContentHelper {
    public function isContentRss(string $content): bool {
        libxml_use_internal_errors(true);

        try {
            $xml = simplexml_load_string($content);
            if ($xml === false) {
                return false;
            }

            $rootName = strtolower($xml->getName());

            return in_array($rootName, ['rss', 'rdf', 'feed'], true);
        } catch (Exception $e) {
            return false;
        }
    }

    public function slugifyTitle($title) {

        $string = preg_replace('/[^\\pL\d_]+/u', '-', $title);
        $string = trim(strtolower($string), '-');
        $string = preg_replace("/[\s-]+/", ' ', $string);
        $string = preg_replace("/[\s_]/", '-', $string);
        $string = preg_replace("/[^a-z0-9\.\-\P{Han}]+/i", '', $string);
        $string = trim(mb_substr($string, 0, 75), '-');

        return $string;
    }

    public function getCanonicalUrl(string $url): string {
        $parts = parse_url(html_entity_decode($url));

        return ($parts['scheme'] ?? 'https') . '://' . $parts['host'] . rtrim($parts['path'] ?? '', '/');
    }
}
