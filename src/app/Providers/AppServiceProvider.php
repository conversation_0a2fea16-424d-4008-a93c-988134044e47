<?php

declare(strict_types=1);

namespace App\Providers;

use App\Modules\Fetcher\Logics\FetchAndProcessEndpointForLocalFetcherLogic;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider {
    /**
     * Register any application services.
     */
    public function register(): void {
        $this->app->bind(FetchAndProcessEndpointForLocalFetcherLogic::class, function ($app) {
            return new FetchAndProcessEndpointForLocalFetcherLogic();
        });

    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void {

    }
}
