<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

use App\Classes\Interfaces\AiModel;
use App\Enums\AiModels;

class GeminiAiModelObject implements AiModel {
    public function __construct(
        protected string $apiKey,
        protected string $modelUrl,
        protected string $userPrompt,
        protected string $systemPrompt,
        protected AiModels $model = AiModels::GEMINI_2_0_FLASH,
        protected float $temperature = 0.7,
    ) {
    }

    public function getModelParameters(): array {
        $parameters = [
            'model'    => $this->model->value,
            'contents' => [
                [
                    'role'  => 'user',
                    'parts' => [
                        ['text' => $this->userPrompt],
                    ],
                ],
            ],
            'systemInstruction' => [
                'role'  => 'system',
                'parts' => [
                    ['text' => $this->systemPrompt],
                ],
            ],
            'generationConfig' => [
                'temperature' => $this->temperature,
            ],
        ];

        return $parameters;
    }

    public function getModelName(): string {
        return $this->model->value;
    }

    public function getModelUrl(): string {
        return $this->modelUrl;
    }

    public function getApiKey(): string {
        return $this->apiKey;
    }
}
