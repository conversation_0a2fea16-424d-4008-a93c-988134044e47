<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

class EndpointObject {
    public function __construct(
        private int $crawlerSettingId,
        private string $endpoint,
        private ?string $defaultCategories,
        private ?string $defaultTopics,
        private bool $hasProxyImage,
    ) {
    }

    public function getCrawlerSettingId(): int {
        return $this->crawlerSettingId;
    }

    public function getEndpoint(): string {
        return $this->endpoint;
    }

    public function getDefaultCategories(): ?string {
        return $this->defaultCategories;
    }

    public function getDefaultTopics(): ?string {
        return $this->defaultTopics;
    }

    public function hasProxyImage(): bool {
        return $this->hasProxyImage;
    }
}
