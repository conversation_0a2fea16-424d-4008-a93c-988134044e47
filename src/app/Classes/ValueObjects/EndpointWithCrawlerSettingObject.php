<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

use App\Models\PublisherEndpoint;

class EndpointWithCrawlerSettingObject {
    public function __construct(
        private PublisherEndpoint $publisherEndpoint,
    ) {
    }

    public function getEndpointId(): string {
        return $this->publisherEndpoint->id;
    }

    public function getCrawlerSettingId(): string {
        return $this->publisherEndpoint->crawler_setting_id;
    }

    public function getPublisherId(): int {
        return $this->publisherEndpoint->crawlerSetting->publisher_id;
    }

    public function getChannelId(): int {
        return $this->publisherEndpoint->crawlerSetting->channel_id;
    }

    public function getHost(): string {
        return $this->publisherEndpoint->crawlerSetting->host;
    }

    public function getEndpoint(): string {
        return $this->publisherEndpoint->endpoint;
    }

    public function getFrequency(): int {
        return $this->publisherEndpoint->crawlerSetting->frequency;
    }

    public function getCustomUserAgent(): ?string {
        return $this->publisherEndpoint->crawlerSetting->custom_user_agent;
    }

    public function getCustomPrompt(): ?string {
        return $this->publisherEndpoint->crawlerSetting->custom_prompt;
    }

    public function useHeadlessBrowser(): bool {
        return $this->publisherEndpoint->crawlerSetting->use_headless_browser;
    }
}
