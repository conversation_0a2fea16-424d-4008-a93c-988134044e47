<?php

declare(strict_types=1);

namespace App\Classes\ValueObjects;

use App\Models\Article;
use App\Models\Channel;
use App\Models\Prediction;
use App\Models\Publisher;

class ArticleDataWithPredictionDataObject {
    public function __construct(
        private Prediction $prediction,
    ) {
    }

    public function getPrediction(): Prediction {
        return $this->prediction;
    }

    public function getArticle(): Article {
        return $this->prediction->article->first();
    }

    public function getChannel(): Channel {
        return $this->prediction->article->channel;
    }

    public function getPublisher(): Publisher {
        return $this->prediction->publisher->first();
    }

    public function getThumbnailURL(): string {
        return $this->prediction->thumbnailURL;
    }

    public function getWideImageWidth(): int {
        return $this->prediction->wideImageWidth;
    }

    public function getWideImageHeight(): int {
        return $this->prediction->wideImageHeight;
    }
}
