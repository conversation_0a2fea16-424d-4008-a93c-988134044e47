<?php

declare(strict_types=1);

namespace App\Classes\Services\Criteria;

use App\Models\PublisherEndpoint;
use Illuminate\Database\Eloquent\Builder;

class CrawlerSettingWithEndpointForLocalFetcher implements Criteriable {
    public function __construct(
        private string $endpointId,
    ) {
    }

    public function apply(Builder $builder): Builder {
        return $builder
            ->with('crawlerSetting')
            ->where('id', $this->endpointId)
            ->whereHas('crawlerSetting', function (Builder $query): void {
                $query->where('enabled', true)
                    ->where('use_smart_crawler', true);
            })
            ->where(function (Builder $query): void {
                $query->whereNull('last_checked')
                    ->orWhere(function (Builder $subQuery): void {
                        $subQuery->whereRaw('NOW() >= TIMESTAMPADD(MINUTE, (SELECT frequency FROM publisher_crawler_settings WHERE publisher_crawler_settings.id = publisher_endpoints.crawler_setting_id), last_checked)');
                    });
            });
    }

    public function getApplicableTo(): ?string {
        return PublisherEndpoint::class;
    }
}
