<?php

declare(strict_types=1);

namespace App\Modules\Fetcher\Services;

use App\Classes\Constants\AiPrompts;
use App\Classes\ValueObjects\RawContentObject;
use App\Helpers\ContentHelper;
use App\Modules\AiModels\AiModelClient;
use App\Repositories\ArticleRepository;

class RetrievesArticleDataFromRawContent {
    public function __construct(
        private ExtractsRssItemsFromRawContent $extractsRssItemsFromRawContent,
        private ArticleRepository $articleRepository,
        private AiModelClient $aiModelClient,
        private ContentHelper $contentHelper,
    ) {
    }

    public function execute(RawContentObject $rawContentObject): array {
        if ($this->contentHelper->isContentRss($rawContentObject->getRawContent()) === true) {
            return $this->extractsRssItemsFromRawContent->execute($rawContentObject->getRawContent());
        }

        $userPrompt   = str_replace(':raw_html', $rawContentObject->getRawContent(), AiPrompts::EXTRACT_ARTICLE_LINKS_FROM_HTML_USER_PROMPT);

        return $this->getNewArticlesFromLinks($this->extractJsonArray($this->aiModelClient->ask(AiPrompts::EXTRACT_ARTICLE_LINKS_FROM_HTML_SYSTEM_PROMPT, $userPrompt)));

    }

    private function extractJsonArray($input): array {
        $startPos = strpos($input, '[');
        $endPos   = strrpos($input, ']');

        if ($startPos !== false && $endPos !== false) {
            $jsonString = substr($input, $startPos, $endPos - $startPos + 1);

            return json_decode($jsonString, true);
        }

        return [];
    }

    private function getNewArticlesFromLinks(array $links): array {
        return array_filter($links, function ($link) {
            return $this->articleRepository->findWhere(['canonicalURL' => $this->contentHelper->getCanonicalUrl($link)]) === null;
        });
    }
}
