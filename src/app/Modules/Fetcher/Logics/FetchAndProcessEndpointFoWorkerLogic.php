<?php

declare(strict_types=1);

namespace App\Modules\Fetcher\Logics;

use App\Classes\ValueObjects\EndpointWithCrawlerSettingObject;
use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Services\PassesArticleDataToParserAdder;
use App\Modules\Fetcher\Services\RetrievesArticleDataFromRawContent;
use App\Repositories\PublisherEndpointRepository;
use App\Services\BypassCloudflareService;
use App\Services\HeadlessBrowserService;
use Illuminate\Support\Facades\Log;

class FetchAndProcessEndpointFoWorkerLogic {
    public function __construct(
        private RetrievesArticleDataFromRawContent $retrievesArticleDataFromRawContentService,
        private BypassCloudflareService $bypassCloudflareService,
        private HeadlessBrowserService $headlessBrowserService,
        private PassesArticleDataToParserAdder $passesArticleDataToParserAdderService,
        private ContentHelper $contentHelper,
        private PublisherEndpointRepository $publisherEndpointsRepository,
    ) {
    }

    public function execute(string $workerId): void {
        $endpointsWithCrawlerSettings = $this->publisherEndpointsRepository->getCrawlerSettingsWithEndpointsByWorkerId($workerId);

        foreach ($endpointsWithCrawlerSettings as $endpoint) {
            $endpointWithCrawlerSettingObject = new EndpointWithCrawlerSettingObject(
                $endpoint
            );

            Log::info('Fetching articles for endpoint ' . $endpointWithCrawlerSettingObject->getEndpoint());
            $rawHtmlOrRss     = $endpointWithCrawlerSettingObject->useHeadlessBrowser() === true ? $this->headlessBrowserService->getRawHtmlOrRss($endpointWithCrawlerSettingObject->getEndpoint(), $endpointWithCrawlerSettingObject->getCustomUserAgent()) : $this->bypassCloudflareService->getRawHtmlOrRss($endpointWithCrawlerSettingObject->getEndpoint(), $endpointWithCrawlerSettingObject->getCustomUserAgent());
            $articleDataArray = $this->retrievesArticleDataFromRawContentService->execute($rawHtmlOrRss);

            if ($articleDataArray === []) {
                Log::info('No article data found for endpoint ' . $endpointWithCrawlerSettingObject->getEndpoint());

                continue;
            }

            $this->passesArticleDataToParserAdderService->execute(
                $endpointWithCrawlerSettingObject->getPublisherId(),
                $endpointWithCrawlerSettingObject->getChannelId(),
                $articleDataArray,
                $this->contentHelper->isContentRss($rawHtmlOrRss->getRawContent()),
                $endpointWithCrawlerSettingObject->getCustomPrompt(),
                $endpointWithCrawlerSettingObject->useHeadlessBrowser()
            );
            Log::info('Successfully processed endpoint ' . $endpointWithCrawlerSettingObject->getEndpoint());
        }
    }
}
