<?php

declare(strict_types=1);

namespace App\Modules\AiModels;

use Google\Auth\ApplicationDefaultCredentials;
use Google\Auth\Credentials\ServiceAccountCredentials;

class GeminiAccessTokenProvider {
    public function execute(): string {
        if (config('google.gemini.key_path')) {
            $credentials = new ServiceAccountCredentials(
                ['https://www.googleapis.com/auth/cloud-platform'],
                config('google.gemini.key_path')
            );
        } else {
            $credentials = ApplicationDefaultCredentials::getCredentials(
                ['https://www.googleapis.com/auth/cloud-platform']
            );
        }
        $authToken = $credentials->fetchAuthToken();

        return $authToken['access_token'];
    }
}
