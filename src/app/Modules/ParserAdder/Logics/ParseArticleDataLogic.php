<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Logics;

use App\Classes\Constants\AiPrompts;
use App\Classes\Constants\Parser;
use App\Exceptions\ParseArticleException;
use App\Modules\AiModels\AiModelClient;
use App\Modules\ParserAdder\Services\SanitizesParsedContent;
use App\Modules\ParserAdder\Services\ValidatesParsedContent;
use App\Services\BypassCloudflareService;
use App\Services\HeadlessBrowserService;
use Illuminate\Http\Request;
use Throwable;

class ParseArticleDataLogic {
    public function __construct(
        private BypassCloudflareService $bypassCloudflareService,
        private HeadlessBrowserService $headlessBrowserService,
        private ValidatesParsedContent $validatesParsedContent,
        private SanitizesParsedContent $sanitizesParsedContent,
        private AiModelClient $aiModelClient,
    ) {
    }

    public function execute(Request $request): array {
        try {
            $publisherId                   = $request->get('publisher_id');
            $articleDataRssItem            = $request->get('article_data_rss_item');
            $articleDataHtmlLink           = $request->get('article_data_html_link');
            $customPrompt                  = $request->get('custom_prompt', '');
            $useHeadlessBrowser            = $request->get('use_headless_browser', false);

            if ($articleDataHtmlLink !== null) {
                $rawArticleData = $useHeadlessBrowser === true ? $this->headlessBrowserService->getRawHtmlOrRss($articleDataHtmlLink)->getRawContent() : $this->bypassCloudflareService->getRawHtmlOrRss($articleDataHtmlLink)->getRawContent();
            } else {
                $rawArticleData = $articleDataRssItem;
            }

            $userPrompt                            = str_replace(':raw_content', $rawArticleData, $customPrompt === null ? AiPrompts::EXTRACT_ARTICLE_CONTENTS_FROM_RAW_CONTENT_USER_PROMPT : $customPrompt);
            $parsedContent                         = $this->extractJson($this->aiModelClient->ask(AiPrompts::EXTRACT_ARTICLE_CONTENTS_FROM_RAW_CONTENT_SYSTEM_PROMPT, $userPrompt));
            $validatedContent                      = $this->validatesParsedContent->execute($parsedContent, $publisherId);
            $sanitizedContent                      = $this->sanitizesParsedContent->execute($validatedContent, $publisherId);
            $sanitizedContent[Parser::CONTENT_MD5] = md5($rawArticleData);

            return $sanitizedContent;
        } catch (Throwable $exception) {
            throw new ParseArticleException($exception->getCode(), $exception->getMessage() . ' Publisher ID: ' . $publisherId);
        }

    }

    private function extractJson($input): array {
        $input = trim($input);
        $input = preg_replace('/^```(?:json)?\s*|\s*```$/i', '', $input);

        return json_decode($input, true);
    }
}
