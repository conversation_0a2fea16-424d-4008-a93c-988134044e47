<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Exceptions\UnauthorisedException;
use Closure;
use Firebase\JWT\JWK;
use Firebase\JWT\JWT;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class Authenticate {

    /**
     * @throws UnauthorisedException
     */
    public function handle(Request $request, Closure $next): JsonResponse {
        if (app()->env !== 'production') {
            return $next($request);
        }

        $authorization = $request->header('Authorization');

        if (!$authorization || !str_starts_with($authorization, 'Bearer ')) {
            throw new UnauthorisedException();
        }

        $jwt = substr($authorization, 7);

        if (!$this->verifyGoogleOidcJwt($jwt)) {
            throw new UnauthorisedException();
        }

        return $next($request);
    }

    private function verifyGoogleOidcJwt(string $jwt): bool
    {
        try {
            $jwkSet = Http::get('https://www.googleapis.com/oauth2/v3/certs')->json();
            $decoded = JWT::decode($jwt, JWK::parseKeySet($jwkSet));

            if ($decoded->iss !== 'https://accounts.google.com') {
                return false;
            }

            $expectedAudience = config('google.pubsub.push_endpoint');
            if ($decoded->aud !== $expectedAudience) {
                return false;
            }

            return true;
        } catch (\Throwable $e) {
            return false;
        }
    }
}
