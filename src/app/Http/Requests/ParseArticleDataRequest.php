<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ParseArticleDataRequest extends FormRequest {
    public function rules(): array {
        return [
            'publisher_id'           => 'required|integer',
            'channel_id'             => 'required|integer',
            'article_data_rss_item'  => 'required_if:article_data_html_link,null|string',
            'article_data_html_link' => 'required_if:article_data_rss_item,null|string',
            'custom_prompt'          => 'nullable|string',
            'use_headless_browser'   => 'nullable|boolean',
        ];
    }
}
