<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Classes\Constants\Parser;
use App\Classes\ValueObjects\ParsedArticleObject;
use App\Http\Requests\ParseArticleDataRequest;
use App\Modules\ParserAdder\Logics\ParseArticleDataLogic;
use App\Modules\ParserAdder\Logics\PopulateArticleDataIntoDatabaseLogic;
use Illuminate\Http\JsonResponse;

class ParserAdderController extends Controller {
    public function parseArticleData(ParseArticleDataRequest $request, ParseArticleDataLogic $parseArticleDataLogic, PopulateArticleDataIntoDatabaseLogic $populateArticleDataIntoDatabaseLogic): JsonResponse {
        $articleParsed = $parseArticleDataLogic->execute($request);
        $populateArticleDataIntoDatabaseLogic->execute(new ParsedArticleObject(
            $articleParsed[Parser::ARTICLE_ID],
            $articleParsed[Parser::TITLE],
            $articleParsed[Parser::DESCRIPTION],
            $articleParsed[Parser::FULL_CONTENT],
            $articleParsed[Parser::AUTHOR],
            $articleParsed[Parser::PUBLISHED_DATE],
            $articleParsed[Parser::MODIFIED_DATE],
            $articleParsed[Parser::LINK],
            $articleParsed[Parser::LINK],
            $articleParsed[Parser::COVER_IMAGE_URL],
            $articleParsed[Parser::MEDIA],
            $articleParsed[Parser::CONTENT_MD5],
        ), $request->get('publisher_id'), $request->get('channel_id'));

        return response()->json();
    }
}
