<?php

declare(strict_types=1);

namespace Tests\Unit\Listeners;

use App\Events\PubSubEvent;
use App\Listeners\PubSubEventHandler;
use App\Services\GoogleCloudPubSubService;
use Mockery;
use Tests\TestCase;

class PubSubEventHandlerTest extends TestCase {
    public function testItHandlePubSubEventHandler(): void {
        $googleCloudPubSubService = Mockery::mock(GoogleCloudPubSubService::class);
        $googleCloudPubSubService->shouldReceive('sendMessage')->once();

        $pubSubEventHandler = new PubSubEventHandler($googleCloudPubSubService);

        $pubSubEvent = Mockery::mock(PubSubEvent::class);
        $pubSubEvent->shouldReceive('getTopic')->andReturn('test');
        $pubSubEvent->shouldReceive('getPayload')->andReturn(['test' => 'test']);

        $pubSubEventHandler->handle($pubSubEvent);
    }
}
