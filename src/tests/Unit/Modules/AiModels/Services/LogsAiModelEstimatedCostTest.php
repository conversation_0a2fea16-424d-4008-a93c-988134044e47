<?php

declare(strict_types=1);

namespace Tests\Modules\AiModels\Services;

use App\Classes\Interfaces\AiModel;
use App\Modules\AiModels\Services\LogsAiModelEstimatedCost;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class LogsAiModelEstimatedCostTest extends TestCase {
    public function testItLogsAiModelEstimatedCost(): void {
        $modelMock = Mockery::mock(AiModel::class);
        $modelMock->shouldReceive('getModelName')->andReturn('gpt-4o-mini');
        $inputTokens        = 3000;
        $outputTokens       = 1500;
        $expectedInputCost  = round(($inputTokens / 1_000_000) * 0.4, 6) * 4;
        $expectedOutputCost = round(($outputTokens / 1_000_000) * 1.6, 6) * 4;
        $expectedTotalCost  = round($expectedInputCost + $expectedOutputCost, 6);

        Log::shouldReceive('info')->once()->with('AI Model Cost', [
            'model'         => 'gpt-4o-mini',
            'input_tokens'  => $inputTokens,
            'output_tokens' => $outputTokens,
            'input_cost'    => 'RM' . $expectedInputCost,
            'output_cost'   => 'RM' . $expectedOutputCost,
            'total_cost'    => 'RM' . $expectedTotalCost,
        ]);

        $logger = new LogsAiModelEstimatedCost();
        $logger->execute($modelMock, $inputTokens, $outputTokens);
    }

    public function testItCalculateThinkingCost(): void {
        $service = new LogsAiModelEstimatedCost();

        $model        = 'gpt-4o-mini';
        $inputTokens  = 5000;
        $expectedCost = round(($inputTokens / 1_000_000) * 0.4, 6) * 4;

        $actualCost = $service->calculateThinkingCost($model, $inputTokens, config('ai_models_costs'));
        $this->assertEquals($expectedCost, $actualCost);
    }

    public function testItCalculateFinalResultCost(): void {
        $service = new LogsAiModelEstimatedCost();

        $model = Mockery::mock(AiModel::class);
        $model->shouldReceive('getModelName')->andReturn('gpt-4o-mini');
        $outputTokens = 5000;
        $expectedCost = round(($outputTokens / 1_000_000) * 1.6, 6) * 4;

        $actualCost = $service->calculateFinalResultCost($model, $outputTokens, config('ai_models_costs'));
        $this->assertEquals($expectedCost, $actualCost);
    }

    public function testItEstimateTotalCostReturnsCorrectValue(): void {
        $service   = new LogsAiModelEstimatedCost();
        $modelMock = Mockery::mock(AiModel::class);
        $modelMock->shouldReceive('getModelName')->andReturn('gpt-4o-mini');

        $inputTokens       = 8000;
        $outputTokens      = 2000;
        $expectedTotalCost = round(
            round(($inputTokens / 1_000_000) * 0.4, 6) * 4 +
            round(($outputTokens / 1_000_000) * 1.6, 6) * 4,
            6
        );

        $actualTotalCost = $service->estimateTotalCost($modelMock, $inputTokens, $outputTokens, config('ai_models_costs'));
        $this->assertEquals($expectedTotalCost, $actualTotalCost);
    }
}
