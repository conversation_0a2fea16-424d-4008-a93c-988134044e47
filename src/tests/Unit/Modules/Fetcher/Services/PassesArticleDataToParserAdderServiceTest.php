<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\Fetcher\Logics;

use App\Events\ArticleDataIsFetched;
use App\Exceptions\APIException;
use App\Modules\Fetcher\Services\PassesArticleDataToParserAdder;
use App\Services\ParserAdderClient;
use Exception;
use Illuminate\Support\Facades\Event;
use Mockery;
use Tests\TestCase;

class PassesArticleDataToParserAdderServiceTest extends TestCase {
    public function testItPassArticleDataToParserAdderService(): void {
        putenv('APP_ENV=local');
        $this->app['env']      = 'local';
        $article               = $this->generator->paragraph();
        $articleData           = [$article];
        $parserAdderClientMock = Mockery::mock(ParserAdderClient::class);
        $parserAdderClientMock
            ->shouldReceive('postArticleDataToParserAdderService')
            ->once()
            ->with(
                1,
                2,
                $article,
                true,
                null,
                false
            );
        $logic = new PassesArticleDataToParserAdder($parserAdderClientMock);
        $logic->execute(1, 2, $articleData, true, null, false);
    }

    public function testItExecuteForNonLocal(): void {
        putenv('APP_ENV=production');
        $this->app['env'] = 'production';

        Event::fake();
        $article     = $this->generator->paragraph();
        $articleData = [$article];

        $parserAdderClientMock = Mockery::mock(ParserAdderClient::class);

        $logic = new PassesArticleDataToParserAdder($parserAdderClientMock);
        $logic->execute(1, 2, $articleData, true, null, false);

        Event::assertDispatched(ArticleDataIsFetched::class, function ($event) use ($article) {
            return $event->getPublisherId() === 1
                   && $event->getChannelId() === 2
                   && $event->getArticleData() === $article
                   && $event->isRss() === true
                   && $event->getCustomPrompt() === ''
                   && $event->useHeadlessBrowser() === false;
        });
    }

    public function testItExecuteThrowsException(): void {
        putenv('APP_ENV=local');
        $this->app['env'] = 'local';

        $this->expectException(APIException::class);

        $parserAdderClientMock = Mockery::mock(ParserAdderClient::class);
        $parserAdderClientMock->shouldReceive('postArticleDataToParserAdderService')
            ->once()
            ->andThrow(new Exception(''));

        $service = new PassesArticleDataToParserAdder($parserAdderClientMock);
        $service->execute(1, 2, ['testIt'], false, null, false);
    }
}
