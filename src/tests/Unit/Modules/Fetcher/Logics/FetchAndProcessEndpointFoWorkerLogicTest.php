<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\Fetcher\Logics;

use App\Classes\ValueObjects\EndpointWithCrawlerSettingObject;
use App\Classes\ValueObjects\RawContentObject;
use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Logics\FetchAndProcessEndpointFoWorkerLogic;
use App\Modules\Fetcher\Services\PassesArticleDataToParserAdder;
use App\Modules\Fetcher\Services\RetrievesArticleDataFromRawContent;
use App\Repositories\PublisherEndpointRepository;
use App\Services\BypassCloudflareService;
use App\Services\HeadlessBrowserService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class FetchAndProcessEndpointFoWorkerLogicTest extends TestCase
{

    public function testItFetchAndProcessEndpointWithWorkerIdUsingHeadlessBrowser(): void
    {

        $workerId = 'w1';
        $fakeArticleUrl = [
            $this->generator->url(),
            $this->generator->url(),
        ];

        $mockCrawlerSettings = $this->createPublisherCrawlerSetting([
            'use_headless_browser' => 1,
            'worker' => $workerId,
        ]);

        $mockEndpointWithCrawlerSettings = $this->createPublisherEndpoints(2, $mockCrawlerSettings->id);
        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->with($workerId)
            ->once()
            ->andReturn($mockEndpointWithCrawlerSettings);

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn('<html><body><h1>Test</h1></body></html>');

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('isContentRss')
            ->twice()
            ->andReturn(false);


        $mockHeadlessBrowserService = Mockery::mock(HeadlessBrowserService::class);
        $mockHeadlessBrowserService->shouldReceive('getRawHtmlOrRss')
            ->andReturn($rawContentObjectMock);

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')
            ->twice()
            ->andReturn($fakeArticleUrl);

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')
            ->andReturn(true);

        $service = new FetchAndProcessEndpointFoWorkerLogic(
            $mockRetrievesArticleDataFromRawContentService,
            Mockery::mock(BypassCloudflareService::class),
            $mockHeadlessBrowserService,
            $mockPassesArticleDataToParserAdderService,
            $mockContentHelper,
            $mockPublisherEndpointRepository
        );
        $service->execute($workerId);
    }

    public function testItFetchAndProcessEndpointWithWorkerIdUsingBypassCloudflare(): void
    {
        $workerId = 'w1';
        $fakeArticleUrl = [
            $this->generator->url(),
            $this->generator->url(),
        ];

        $mockCrawlerSettings = $this->createPublisherCrawlerSetting([
            'use_headless_browser' => 0,
            'worker' => $workerId,
        ]);

        $mockEndpointWithCrawlerSettings = $this->createPublisherEndpoints(2, $mockCrawlerSettings->id);
        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->with($workerId)
            ->once()
            ->andReturn($mockEndpointWithCrawlerSettings);

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn('<html><body><h1>Test</h1></body></html>');

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('isContentRss')
            ->twice()
            ->andReturn(false);

        $mockBypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $mockBypassCloudflareService->shouldReceive('getRawHtmlOrRss')
            ->twice()
            ->andReturn($rawContentObjectMock);

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')
            ->twice()
            ->andReturn($fakeArticleUrl);

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')
            ->twice()
            ->andReturn(true);

        $service = new FetchAndProcessEndpointFoWorkerLogic(
            $mockRetrievesArticleDataFromRawContentService,
            $mockBypassCloudflareService,
            Mockery::mock(HeadlessBrowserService::class),
            $mockPassesArticleDataToParserAdderService,
            $mockContentHelper,
            $mockPublisherEndpointRepository
        );
        $service->execute($workerId);
    }

    public function testItFetchAndProcessEndpointWithInvalidWorkerId(): void
    {
        $workerId = 'w1';

        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->with($workerId)
            ->once()
            ->andReturn(Collection::empty());


        $service = new FetchAndProcessEndpointFoWorkerLogic(
            Mockery::mock(RetrievesArticleDataFromRawContent::class),
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(PassesArticleDataToParserAdder::class),
            Mockery::mock(ContentHelper::class),
            $mockPublisherEndpointRepository
        );
        $service->execute($workerId);
    }

    public function testItFetchAndProcessEndpointWithNoArticleData(): void
    {
        $workerId = 'w1';
        $fakeArticleUrl = [
            $this->generator->url(),
            $this->generator->url(),
        ];

        $mockCrawlerSettings = $this->createPublisherCrawlerSetting([
            'use_headless_browser' => 1,
            'worker' => $workerId,
        ]);

        $mockEndpointWithCrawlerSettings = $this->createPublisherEndpoints(2, $mockCrawlerSettings->id);
        $mockPublisherEndpointRepository = Mockery::mock(PublisherEndpointRepository::class);
        $mockPublisherEndpointRepository->shouldReceive('getCrawlerSettingsWithEndpointsByWorkerId')
            ->with($workerId)
            ->once()
            ->andReturn($mockEndpointWithCrawlerSettings);

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn('<html><body><h1>Test</h1></body></html>');

        $mockContentHelper = Mockery::mock(ContentHelper::class);

        $mockHeadlessBrowserService = Mockery::mock(HeadlessBrowserService::class);
        $mockHeadlessBrowserService->shouldReceive('getRawHtmlOrRss')
            ->andReturn($rawContentObjectMock);

        $mockRetrievesArticleDataFromRawContentService = Mockery::mock(RetrievesArticleDataFromRawContent::class);
        $mockRetrievesArticleDataFromRawContentService->shouldReceive('execute')
            ->twice()
            ->andReturn([]);

        $mockPassesArticleDataToParserAdderService = Mockery::mock(PassesArticleDataToParserAdder::class);
        $mockPassesArticleDataToParserAdderService->shouldReceive('execute')
            ->never();

        $service = new FetchAndProcessEndpointFoWorkerLogic(
            $mockRetrievesArticleDataFromRawContentService,
            Mockery::mock(BypassCloudflareService::class),
            $mockHeadlessBrowserService,
            $mockPassesArticleDataToParserAdderService,
            $mockContentHelper,
            $mockPublisherEndpointRepository
        );
        $service->execute($workerId);
    }

}
