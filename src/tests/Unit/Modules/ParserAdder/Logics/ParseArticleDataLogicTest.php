<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Logics;

use App\Classes\ValueObjects\RawContentObject;
use App\Exceptions\ParseArticleException;
use App\Modules\AiModels\AiModelClient;
use App\Modules\ParserAdder\Logics\ParseArticleDataLogic;
use App\Modules\ParserAdder\Services\SanitizesParsedContent;
use App\Modules\ParserAdder\Services\ValidatesParsedContent;
use App\Services\BypassCloudflareService;
use App\Services\HeadlessBrowserService;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;

class ParseArticleDataLogicTest extends TestCase {
    public function testItParseArticleDataFromRss(): void {

        $mockRequest = Mockery::mock(Request::class);
        $mockRequest->shouldReceive('get')->with('publisher_id')->andReturn(1);
        $mockRequest->shouldReceive('get')->with('article_data_rss_item')->andReturn('<raw html content>');
        $mockRequest->shouldReceive('get')->with('article_data_html_link')->andReturn(null);
        $mockRequest->shouldReceive('get')->with('custom_prompt', '')->andReturn('');
        $mockRequest->shouldReceive('get')->with('use_headless_browser', false)->andReturn(false);

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
        ];

        $mockAiModelClient = Mockery::mock(AiModelClient::class);
        $mockAiModelClient->shouldReceive('ask')->once()->andReturn(json_encode($mockData));

        $sanitizedData                = $mockData;
        $sanitizedData['content_md5'] = md5('<raw html content>');

        $mockValidator = Mockery::mock(ValidatesParsedContent::class);
        $mockValidator->shouldReceive('execute')->once()->with(
            $mockData, 1
        )->andReturn($mockData);

        $mockSanitizer = Mockery::mock(SanitizesParsedContent::class);
        $mockSanitizer->shouldReceive('execute')->once()->with(
            $mockData, 1
        )->andReturn($mockData);

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            $mockValidator,
            $mockSanitizer,
            $mockAiModelClient
        );
        $result = $logic->execute($mockRequest);
        $this->assertEquals($sanitizedData, $result);
    }

    public function testItParseArticleDataFromHtmlLinkUsingHeadlessBrowser(): void {
        $mockRequest = Mockery::mock(Request::class);
        $mockRequest->shouldReceive('get')->with('publisher_id')->andReturn(1);
        $mockRequest->shouldReceive('get')->with('article_data_rss_item')->andReturn(null);
        $mockRequest->shouldReceive('get')->with('article_data_html_link')->andReturn($this->generator->url());
        $mockRequest->shouldReceive('get')->with('custom_prompt', '')->andReturn('');
        $mockRequest->shouldReceive('get')->with('use_headless_browser', false)->andReturn(true);

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
        ];

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn($mockData['full_content']);

        $headlessBrowserService = Mockery::mock(HeadlessBrowserService::class);
        $headlessBrowserService->shouldReceive('getRawHtmlOrRss')->once()->andReturn($rawContentObjectMock);

        $mockAiModelClient = Mockery::mock(AiModelClient::class);
        $mockAiModelClient->shouldReceive('ask')->once()->andReturn(json_encode($mockData));

        $sanitizedData                = $mockData;
        $sanitizedData['content_md5'] = md5($mockData['full_content']);

        $mockValidator = Mockery::mock(ValidatesParsedContent::class);
        $mockValidator->shouldReceive('execute')->once()->with(
            $mockData, 1
        )->andReturn($mockData);

        $mockSanitizer = Mockery::mock(SanitizesParsedContent::class);
        $mockSanitizer->shouldReceive('execute')->once()->with(
            $mockData, 1
        )->andReturn($mockData);

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            $headlessBrowserService,
            $mockValidator,
            $mockSanitizer,
            $mockAiModelClient
        );
        $result = $logic->execute($mockRequest);
        $this->assertEquals($sanitizedData, $result);
    }

    public function testItParseArticleDataFromHtmlLinkUsingBypassCloudflare(): void {
        $mockRequest = Mockery::mock(Request::class);
        $mockRequest->shouldReceive('get')->with('publisher_id')->andReturn(1);
        $mockRequest->shouldReceive('get')->with('article_data_rss_item')->andReturn(null);
        $mockRequest->shouldReceive('get')->with('article_data_html_link')->andReturn($this->generator->url());
        $mockRequest->shouldReceive('get')->with('custom_prompt', '')->andReturn('');
        $mockRequest->shouldReceive('get')->with('use_headless_browser', false)->andReturn(false);

        $mockData = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
            'link'            => $this->generator->url(),
            'cover_image_url' => $this->generator->url(),
            'media'           => [],
        ];

        $rawContentObjectMock =  Mockery::mock(RawContentObject::class);
        $rawContentObjectMock->shouldReceive('getRawContent')->andReturn($mockData['full_content']);

        $bypassCloudflareService = Mockery::mock(BypassCloudflareService::class);
        $bypassCloudflareService->shouldReceive('getRawHtmlOrRss')->once()->andReturn($rawContentObjectMock);

        $mockAiModelClient = Mockery::mock(AiModelClient::class);
        $mockAiModelClient->shouldReceive('ask')->once()->andReturn(json_encode($mockData));

        $sanitizedData                = $mockData;
        $sanitizedData['content_md5'] = md5($mockData['full_content']);

        $mockValidator = Mockery::mock(ValidatesParsedContent::class);
        $mockValidator->shouldReceive('execute')->once()->with(
            $mockData, 1
        )->andReturn($mockData);

        $mockSanitizer = Mockery::mock(SanitizesParsedContent::class);
        $mockSanitizer->shouldReceive('execute')->once()->with(
            $mockData, 1
        )->andReturn($mockData);

        $logic = new ParseArticleDataLogic(
            $bypassCloudflareService,
            Mockery::mock(HeadlessBrowserService::class),
            $mockValidator,
            $mockSanitizer,
            $mockAiModelClient
        );
        $result = $logic->execute($mockRequest);
        $this->assertEquals($sanitizedData, $result);
    }

    public function testItParseArticleDataThrowsException(): void {
        $mockRequest = Mockery::mock(Request::class);
        $mockRequest->shouldReceive('get')->with('publisher_id')->andReturn(1);
        $mockRequest->shouldReceive('get')->with('article_data_rss_item')->andReturn(null);
        $mockRequest->shouldReceive('get')->with('article_data_html_link')->andReturn(null);
        $mockRequest->shouldReceive('get')->with('custom_prompt', '')->andReturn('');
        $mockRequest->shouldReceive('get')->with('use_headless_browser', false)->andReturn(false);

        $this->expectException(ParseArticleException::class);

        $logic = new ParseArticleDataLogic(
            Mockery::mock(BypassCloudflareService::class),
            Mockery::mock(HeadlessBrowserService::class),
            Mockery::mock(ValidatesParsedContent::class),
            Mockery::mock(SanitizesParsedContent::class),
            Mockery::mock(AiModelClient::class)
        );
        $logic->execute($mockRequest);
    }
}
