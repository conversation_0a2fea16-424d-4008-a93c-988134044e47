<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Repositories\PredictionsRepository;
use Mockery;
use Tests\TestCase;

class CreatesPredictionTest extends TestCase {
    public function testItCreatePrediction(): void {

        $channel = $this->createChannel([
            'language' => 'en',
        ]);

        $mockArticle = $this->createArticle([
            'channelID' => $channel->id,
        ]);

        $expectedPrediction = $this->createPrediction([
            'unique_id' => $mockArticle->uniqueID,
        ]);

        $predictionsRepositoryMock = Mockery::mock(PredictionsRepository::class);
        $predictionsRepositoryMock->shouldReceive('findWhere')
            ->with(['unique_id' => $mockArticle->uniqueID])
            ->once()
            ->andReturn(null);
        $predictionsRepositoryMock->shouldReceive('create')
            ->once()
            ->andReturn($expectedPrediction);

        $service = new CreatesPrediction($predictionsRepositoryMock);
        $service->execute($mockArticle);
    }

    public function testItDoesNotCreatePredictionWhenPredictionExists(): void {
        $mockArticle = $this->createArticle();

        $predictionsRepositoryMock = Mockery::mock(PredictionsRepository::class);
        $predictionsRepositoryMock->shouldReceive('findWhere')
            ->with(['unique_id' => $mockArticle->uniqueID])
            ->once()
            ->andReturn($mockArticle);

        $service = new CreatesPrediction($predictionsRepositoryMock);
        $service->execute($mockArticle);
    }
}
