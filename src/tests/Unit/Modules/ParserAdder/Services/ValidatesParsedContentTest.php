<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Exceptions\ServiceException;
use App\Helpers\MediaHelper;
use App\Modules\ParserAdder\Services\ValidatesParsedContent;
use App\Repositories\PublisherRepository;
use Mockery;
use Tests\TestCase;

class ValidatesParsedContentTest extends TestCase {
    public function testItValidatesParsedContent(): void {

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = new ValidatesParsedContent($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals($parsedContent, $validatedContent);
    }

    public function testItValidatesParsedContentWithoutDescription(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $expectedDescription = substr(strip_tags($parsedContent['full_content']), 0, 150) . '...';

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = new ValidatesParsedContent($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals($expectedDescription, $validatedContent['description']);
    }

    public function testItValidatesParsedContentWithoutAuthor(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $publisherRepositoryMock->shouldReceive('findWhere')
            ->once()
            ->andReturn($mockPublisher);

        $service          = new ValidatesParsedContent($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals($mockPublisher->name, $validatedContent['author']);
    }

    public function testItValidatesParsedContentWithoutCoverImage(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'media'          => [
                [
                    'url'     => $this->generator->url(),
                    'caption' => $this->generator->sentence(),
                ],
            ],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $mediaHelperMock->shouldReceive('isImageUrl')
            ->once()
            ->andReturn(true);

        // $mediaHelperMock->shouldReceive('isVideoUrl')
        //     ->once()
        //     ->andReturn(false);

        $service          = new ValidatesParsedContent($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals($parsedContent['media'][0], $validatedContent['cover_image_url']);
    }

    public function testItValidatesParsedContentWithoutCoverImageAndMedia(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = new ValidatesParsedContent($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals(['url' => '', 'caption' => null], $validatedContent['cover_image_url']);
    }

    public function testItValidatesParsedContentWithEmptyMediaUrl(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'media'          => [
                [
                    'url'     => '',
                    'caption' => $this->generator->sentence(),
                ],
            ],
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => null,
            ],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = new ValidatesParsedContent($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals([], $validatedContent['media']);
    }

    public function testItMissingRequiredField() : void {
        $parsedContent = [];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service = new ValidatesParsedContent($publisherRepositoryMock, $mediaHelperMock);

        $this->expectException(ServiceException::class);
        $service->execute($parsedContent, $mockPublisher->id);
    }
}
