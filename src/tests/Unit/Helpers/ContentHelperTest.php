<?php

declare(strict_types=1);

namespace Tests\Unit\Helpers;

use App\Helpers\ContentHelper;
use PHPUnit\Framework\TestCase;

class ContentHelperTest extends TestCase {
    public function testItIsContentRss(): void {
        $contentHelper = new ContentHelper();

        $this->assertTrue($contentHelper->isContentRss('<rss version="2.0"></rss>'));
        $this->assertTrue($contentHelper->isContentRss('<feed></feed>'));

        $this->assertFalse($contentHelper->isContentRss('<html></html>'));
        $this->assertFalse($contentHelper->isContentRss('<!DOCTYPE html><html></html>'));
        $this->assertFalse($contentHelper->isContentRss(''));
    }

    public function testItSlugifyTitle(): void {
        $contentHelper = new ContentHelper();

        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World?'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World.'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World...'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World???'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...!'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...?!'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...?!?'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...?!?!'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...?!?!?'));
    }

    public function testItGetCanonicalUrl(): void {
        $contentHelper = new ContentHelper();

        $this->assertEquals('https://example.com', $contentHelper->getCanonicalUrl('https://example.com'));
        $this->assertEquals('https://example.com', $contentHelper->getCanonicalUrl('https://example.com/'));
        $this->assertEquals('https://example.com/path', $contentHelper->getCanonicalUrl('https://example.com/path'));
        $this->assertEquals('https://example.com/path', $contentHelper->getCanonicalUrl('https://example.com/path/'));
        $this->assertEquals('https://example.com/path/to/page', $contentHelper->getCanonicalUrl('https://example.com/path/to/page'));
        $this->assertEquals('https://example.com/path/to/page', $contentHelper->getCanonicalUrl('https://example.com/path/to/page?query=string'));
        $this->assertEquals('https://example.com/path/to/page', $contentHelper->getCanonicalUrl('https://example.com/path/to/page?query=string#anchor'));
    }
}
