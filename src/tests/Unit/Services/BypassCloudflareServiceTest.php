<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Classes\Constants\ServerParameters;
use App\Exceptions\APIException;
use App\Services\BypassCloudflareService;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use Mockery;
use Tests\TestCase;
use GuzzleException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use Illuminate\Http\Client\Response as ClientResponse;

class BypassCloudflareServiceTest extends TestCase {
    public function testItGetRawHtmlOrRss(): void {

        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $fakeResponse = new Response(200, [], '<html><body><h1>Test</h1></body></html>');

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->with(
                ServerParameters::HTTP_METHOD_POST,
                Mockery::type('string'),
                [
                    'headers' => [
                        'Content-Type'  => 'application/json',
                    ],
                    'json' => [
                        'url'        => $endpointUrl,
                        'user_agent' => $customUserAgent,
                    ],
                ]
            )
            ->andReturn($fakeResponse);

        $service = new BypassCloudflareService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
        $this->assertEquals($fakeResponse->getBody(), $result->getRawContent());
    }

    public function testItThrowsExceptionAtEmptyContent(): void {
        $this->expectException(APIException::class);
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $fakeResponse = new Response(200, [], '');

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->with(
                ServerParameters::HTTP_METHOD_POST,
                Mockery::type('string'),
                [
                    'headers' => [
                        'Content-Type'  => 'application/json',
                    ],
                    'json' => [
                        'url'        => $endpointUrl,
                        'user_agent' => $customUserAgent,
                    ],
                ]
            )
            ->andReturn($fakeResponse);

        $service = new BypassCloudflareService($clientMock);
        $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
    }

    public function testItThrowsExceptionWhenMakeRequestFails(): void {
        $this->expectException(APIException::class);
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->with(
                ServerParameters::HTTP_METHOD_POST,
                Mockery::type('string'),
                [
                    'headers' => [
                        'Content-Type'  => 'application/json',
                    ],
                    'json' => [
                        'url'        => $endpointUrl,
                        'user_agent' => $customUserAgent,
                    ],
                ]
            )
            ->andThrow(new RequestException('failed', new Request('500', 'Failed')));

        $service = new BypassCloudflareService($clientMock);
        $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
    }
}
