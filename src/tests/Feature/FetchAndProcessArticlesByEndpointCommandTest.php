<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\PublisherCrawlerSetting;
use App\Models\PublisherEndpoint;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FetchAndProcessArticlesByEndpointCommandTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
{
    parent::setUp();

    $this->app->bind(
        \App\Modules\Fetcher\Logics\FetchAndProcessEndpointForLocalFetcherLogic::class,
        function ($app) {
            return new \App\Modules\Fetcher\Logics\FetchAndProcessEndpointForLocalFetcherLogic();
        }
    );
}
    

    public function testItExecutesFetchAndProcessLogicWithCorrectEndpointId(): void
    {
        $publisherCrawlerSetting = $this->createPublisherCrawlerSetting();
        $publisherEndpoint = $this->createPublisherEndpoints(1, $publisherCrawlerSetting->id)->first();

        // Act: run the real command via Artisan kernel
        $exitCode = $this->artisan('fetch-and-process-articles-by-endpoint', [
            'publisherEndpointId' => $publisherEndpoint->id,
        ])->assertExitCode(0);
    }
}
