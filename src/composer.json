{"name": "newswav/newswav-smart-crawler", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "bugsnag/bugsnag-laravel": "^2.29", "firebase/php-jwt": "^6.11", "google/cloud-pubsub": "^2.0", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.10", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "league/fractal": "^0.20.2", "newswav/laravel-central-schema": "^0.0.47"}, "require-dev": {"fakerphp/faker": "^1.9.1", "friendsofphp/php-cs-fixer": "^3.75", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "csfix": ["vendor/bin/php-cs-fixer fix app config database routes tests --using-cache=no --verbose --config=.php-cs-fixer.dist.php"], "csfix-dry": ["vendor/bin/php-cs-fixer fix app config database routes tests --dry-run --verbose --using-cache=no --config=.php-cs-fixer.dist.php"], "phpstan": ["vendor/bin/phpstan analyse"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": [{"type": "vcs", "url": "https://github.com/Newswav/laravel-central-schema"}]}