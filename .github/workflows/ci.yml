name: ci

on:
  pull_request: {}

jobs:
  ci:
    runs-on: ubuntu-latest

    services:
      mysql-service:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: newswav
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - uses: actions/checkout@v2

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3.2'
          tools: composer:v2

      - name: Install Composer dependencies
        run: |
          echo '{"github-oauth": {"github.com": "${{ secrets.GH_NPM_TOKEN }}"}}' > $GITHUB_WORKSPACE/src/auth.json
          cd src
          composer install --no-progress --prefer-dist

      - name: Laravel Initialisation
        run: |
          cp src/.env.testing.example src/.env
          cd src
          php artisan key:generate

      - name: Linting & Analysis
        run: |
          cd src
          vendor/bin/php-cs-fixer fix app config database routes tests --dry-run --using-cache=no --config=.php-cs-fixer.dist.php

      - name: Run Migrations & Tests
        run: | 
          cd src 
          php artisan migrate:fresh --seed
          php artisan test
        env:
          DB_HOST: 127.0.0.1
          DB_DATABASE: newswav
          DB_USERNAME: root
          DB_PASSWORD: password
          JWT_KEY: test

