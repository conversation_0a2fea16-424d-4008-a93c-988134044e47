name: Bugsnag Deployment Notification

on:
  push:
    branches:
      - main

jobs:
  bugsnag_deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Notify Bugsnag of deployment
        run: |
          curl -d "apiKey=${{ secrets.NEWSWAV_SMART_CRAWLER_BUGSNAG_API_KEY }}&appVersion=${{ github.sha }}" https://notify.bugsnag.com/deploy
