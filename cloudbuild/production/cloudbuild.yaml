timeout: 1800s
substitutions:
  _PROJECT_NAME: newswav-smart-crawler
  _PROJECT_APP_NAME: newswav-smart-crawler
  _NAMESPACE: newswav
  _CUSTOM_REGION: asia-southeast1
  _CUSTOM_CLUSTER: prod-cluster-asse1-nw
  _IMAGE_TAG: $COMMIT_SHA

steps:
  - name: gcr.io/kaniko-project/executor
    id: Build
    secretEnv:
      - _COMPOSER_GITHUB_TOKEN
    args:
      - --destination=asia-southeast1-docker.pkg.dev/bustling-sunset-220007/newswav-smart-crawler/newswav-smart-crawler:$COMMIT_SHA
      - --cache=true
      - --build-arg=_COMPOSER_GITHUB_TOKEN

  - name: "asia-southeast1-docker.pkg.dev/bustling-sunset-220007/helm/helm:3.9.3"
    id: Sync secret
    args:
      - upgrade
      - "--install"
      - "--atomic"
      - newswav-smart-crawler-gcpsm
      - "--namespace"
      - $_NAMESPACE
      - "--timeout"
      - 10m
      - "--history-max"
      - "2"
      - "-f"
      - cloudbuild/production/secret.yaml
      - >-
        oci://asia-southeast1-docker.pkg.dev/nw-development-329802/newswav-helm/newswav-app
      - "--version"
      - 0.2.0
    env:
    - 'CLOUDSDK_COMPUTE_REGION=$_CUSTOM_REGION'
    - 'CLOUDSDK_CONTAINER_CLUSTER=$_CUSTOM_CLUSTER'
    waitFor: ["-"]

  - name: 'asia-southeast1-docker.pkg.dev/bustling-sunset-220007/helm/helm:3.9.3'
    id: deploy-crawler-cronjob
    args:
      - upgrade
      - '--install'
      - '--atomic'
      - '--debug'
      - newswav-smart-crawler-worker
      - '--namespace'
      - $_NAMESPACE
      - '--timeout'
      - 10m
      - '--history-max'
      - '2'
      - '-f'
      - cloudbuild/production/values.yaml
      - '--set'
      - image.tag=$_IMAGE_TAG
      - >-
        oci://asia-southeast1-docker.pkg.dev/nw-development-329802/newswav-helm/newswav-app
      - '--version'
      - 0.2.0
    env:
      - 'CLOUDSDK_COMPUTE_REGION=$_CUSTOM_REGION'
      - 'CLOUDSDK_CONTAINER_CLUSTER=$_CUSTOM_CLUSTER'
    waitFor: ["Build", "Sync secret"]

  - name: "gcr.io/cloud-builders/gcloud"
    id: deploy-parser-adder-api
    args:
      - run
      - deploy
      - $_PROJECT_APP_NAME
      - --image=asia-southeast1-docker.pkg.dev/$PROJECT_ID/$_PROJECT_NAME/$_PROJECT_APP_NAME:$COMMIT_SHA
      - --region=asia-southeast1
      - --network=prod-vpc-asse1-nw
      - --subnet=prod-subnet-asse1-nw
      - --platform=managed
      - --allow-unauthenticated
      - --project=$PROJECT_ID
      - --port=80
      - --service-account=<EMAIL>
      - --env-vars-file=cloudbuild/production/config-variables.yaml
      - --update-secrets=/secrets/secret.json=newswav-smart-crawler-gcpsm:latest
    waitFor: ["Build"]

availableSecrets:
  secretManager:
    - versionName: projects/*************/secrets/github-read-private-package_withUsername/versions/latest
      env: "_COMPOSER_GITHUB_TOKEN"
