FROM php:8.3-fpm

RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    nginx \
    netcat-traditional \
    jq

WORKDIR /var/www/html
ADD ./src /var/www/html

RUN rm /etc/nginx/sites-enabled/default

COPY ./configurations/php.ini /usr/local/etc/php/conf.d
COPY ./configurations/nginx.conf /etc/nginx/conf.d/default.conf
COPY ./entrypoint /var/www/entrypoint

RUN chown -R www-data:www-data /var/www
RUN chmod -R 755 /var/www/html/storage
RUN chmod +x /var/www/html/artisan
RUN chmod +x /var/www/entrypoint

ARG _COMPOSER_GITHUB_TOKEN
RUN composer config --global http-basic.github.com ${_COMPOSER_GITHUB_TOKEN}
RUN COMPOSER_MEMORY_LIMIT=-1 composer install

RUN chmod -R 777 /var/www/html/storage
RUN chmod -R 777 /var/www/html/bootstrap

ENTRYPOINT ["/var/www/entrypoint"]

EXPOSE 80
